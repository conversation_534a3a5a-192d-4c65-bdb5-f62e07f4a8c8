import React, { useState, useEffect } from "react";
import ReactD<PERSON> from "react-dom";
import { IoCloseCircleOutline } from "react-icons/io5";
import { CaretLeft, CaretRight, Plus, Trash } from "@phosphor-icons/react";
import TimePicker from "./TimePicker";

interface TimeSlot {
  startTime: string;
  endTime: string;
  duration?: number;
}

interface DatePickerModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (date: string, timeSlots: TimeSlot[], specificDayId?: string) => void;
  editDate?: string;
  editTimeSlots?: TimeSlot[];
  specificDayId?: string; // ID of the specific day being edited
}

const months = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
];

// Available durations in minutes - keep in sync with DurationSelector component


const DatePickerModal: React.FC<DatePickerModalProps> = ({
  open,
  onClose,
  onSave,
  editDate,
  editTimeSlots,
  specificDayId,
}) => {
  const [container, setContainer] = useState<HTMLDivElement | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([
    { startTime: "08:00", endTime: "08:15", duration: 15 }
  ]);
  const [durations, setDurations] = useState<{ [key: number]: number }>({
    0: 15
  });
  const [timeSlotErrors, setTimeSlotErrors] = useState<{ [key: number]: string }>({});

  useEffect(() => {
    if (typeof document !== "undefined") {
      // Ensure that the container exists in the DOM
      const portalContainer = document.getElementById("__next");
      if (!portalContainer) {
        const newContainer = document.createElement("div");
        newContainer.id = "portal-root";
        document.body.appendChild(newContainer);
        setContainer(newContainer);
      } else {
        setContainer(portalContainer as HTMLDivElement);
      }
    }
  }, []);

  useEffect(() => {
    if (typeof document !== "undefined") {
      const scrollbarElement = document.body;

      // Hide scrollbar when modal is open
      if (open && scrollbarElement.style.overflow !== "hidden") {
        scrollbarElement.setAttribute("style", "overflow: hidden");
      }

      // Close modal on 'Esc' key press
      const handleEscKeyPress = (event: KeyboardEvent) => {
        if (event.key === "Escape" && open) {
          onClose(); // Close modal
        }
      };

      document.addEventListener("keydown", handleEscKeyPress);

      // Cleanup the event listener and restore the scrollbar
      return () => {
        document.removeEventListener("keydown", handleEscKeyPress);
        if (open) {
          scrollbarElement.setAttribute("style", "overflow: auto");
        }
      };
    }
  }, [open, onClose]);

  // Reset state when modal opens or handle edit mode
  useEffect(() => {
    if (open) {
      // Always reset durations to default when modal opens
      setDurations({ 0: 15 });

      if (editDate && editTimeSlots && editTimeSlots.length > 0) {

        // Parse the date string - handle different formats
        let dateObj: Date;

        if (editDate.includes(", ")) {
          // Format: "DD, Month, YYYY"
          const [day, month, year] = editDate.split(", ");
          const monthIndex = months.indexOf(month);
          dateObj = new Date(parseInt(year), monthIndex, parseInt(day));
        } else if (editDate.includes("-")) {
          // Format: "DD-MM-YYYY" or similar
          const parts = editDate.split("-");
          if (parts.length === 3) {
            // Assuming day-month-year format
            dateObj = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
          } else {
            // Fallback to current date
            dateObj = new Date();
          }
        } else {
          // Try to parse as a date string
          dateObj = new Date(editDate);
          if (isNaN(dateObj.getTime())) {
            // If parsing fails, use current date
            dateObj = new Date();
          }
        }

        setCurrentDate(dateObj);
        setSelectedDate(dateObj);

        // Validate and add duration to each time slot
        const validTimeSlots = editTimeSlots.filter(slot => {
          // Check if the slot has valid startTime and endTime
          return slot && slot.startTime && slot.endTime;
        });

        // If there are no valid time slots, add a default one
        if (validTimeSlots.length === 0) {
          console.warn("No valid time slots found in edit data, using default");
          setTimeSlots([{ startTime: "08:00", endTime: "08:15", duration: 15 }]);
          setDurations({ 0: 15 });
          return;
        }

        // Add duration to each time slot if not already present
        setTimeSlots(validTimeSlots.map(slot => {
          if (slot.duration) return slot;

          try {
            // Calculate duration if not present
            const [startHour, startMinute] = slot.startTime.split(":").map(Number);
            const [endHour, endMinute] = slot.endTime.split(":").map(Number);

            const startTimeInMinutes = startHour * 60 + startMinute;
            let endTimeInMinutes = endHour * 60 + endMinute;

            // Handle case where end time is on the next day
            if (endTimeInMinutes < startTimeInMinutes) {
              endTimeInMinutes += 24 * 60; // Add 24 hours in minutes
            }

            return {
              ...slot,
              duration: endTimeInMinutes - startTimeInMinutes
            };
          } catch (error) {
            console.error("Error calculating duration for slot:", slot, error);
            // Return slot with default duration
            return {
              ...slot,
              duration: 15
            };
          }
        }));

        // Set durations based on time differences
        const newDurations: { [key: number]: number } = {};
        validTimeSlots.forEach((slot, index) => {
          try {
            if (!slot.startTime || !slot.endTime) {
              console.warn("Invalid time slot at index", index, "- missing startTime or endTime");
              newDurations[index] = 15; // Default duration
              return;
            }

            // If the slot already has a duration, use it
            if (slot.duration) {
              newDurations[index] = slot.duration;
              return;
            }

            // Calculate duration from start and end times
            const [startHour, startMinute] = slot.startTime.split(":").map(Number);
            const [endHour, endMinute] = slot.endTime.split(":").map(Number);

            const startTimeInMinutes = startHour * 60 + startMinute;
            let endTimeInMinutes = endHour * 60 + endMinute;

            // Handle cases where end time is on the next day
            if (endTimeInMinutes < startTimeInMinutes) {
              endTimeInMinutes += 24 * 60; // Add 24 hours in minutes
            }

            const calculatedDuration = endTimeInMinutes - startTimeInMinutes;
            newDurations[index] = calculatedDuration;
          } catch (error) {
            console.error("Error calculating duration for slot at index", index, error);
            newDurations[index] = 15; // Default duration
          }
        });

        setDurations(newDurations);
        // Clear any existing errors
        setTimeSlotErrors({});
      } else {
        // Default new mode
        setCurrentDate(new Date());
        setSelectedDate(null);
        setTimeSlots([{ startTime: "08:00", endTime: "08:15", duration: 15 }]);
        setDurations({ 0: 15 });
        setTimeSlotErrors({});
      }
    }
  }, [open, editDate, editTimeSlots]);

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const handlePrevMonth = () => {
    setCurrentDate(prev => {
      const prevMonth = new Date(prev);
      prevMonth.setMonth(prev.getMonth() - 1);
      return prevMonth;
    });
  };

  const handleNextMonth = () => {
    setCurrentDate(prev => {
      const nextMonth = new Date(prev);
      nextMonth.setMonth(prev.getMonth() + 1);
      return nextMonth;
    });
  };

  const handleDateClick = (day: number) => {
    const newDate = new Date(currentDate);
    newDate.setDate(day);
    setSelectedDate(newDate);

    // When a new date is selected, ensure we have the default time slot with 15 minutes duration
    if (timeSlots.length === 0) {
      setTimeSlots([{ startTime: "08:00", endTime: "08:15", duration: 15 }]);
      setDurations({ 0: 15 });
      setTimeSlotErrors({});
    } else {
      // Ensure all durations are properly set to their default values
      const updatedDurations = { ...durations };
      timeSlots.forEach((_, index) => {
        if (!updatedDurations[index] || updatedDurations[index] === 240) {
          updatedDurations[index] = 15;
        }
      });
      setDurations(updatedDurations);
      // Clear errors when selecting a new date
      setTimeSlotErrors({});
    }
  };

  const handleAddTimeSlot = () => {
    const newIndex = timeSlots.length;

    // Determine the start time for the new slot
    let newStartTime = "08:00"; // Default start time
    let newEndTime = "08:15"; // Default end time
    const defaultDuration = 15; // Default duration in minutes

    // If there are existing time slots, use the end time of the last slot as the start time
    if (timeSlots.length > 0) {
      const lastSlot = timeSlots[timeSlots.length - 1];
      newStartTime = lastSlot.endTime;

      // Calculate the new end time based on the start time and default duration
      const [startHour, startMinute] = newStartTime.split(":").map(Number);
      const startTimeInMinutes = startHour * 60 + startMinute;
      const totalMinutes = startTimeInMinutes + defaultDuration;
      const hour = Math.floor(totalMinutes / 60) % 24;
      const minute = totalMinutes % 60;
      const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
      const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
      newEndTime = `${formattedHour}:${formattedMinute}`;
    }

    setTimeSlots(prev => [...prev, { startTime: newStartTime, endTime: newEndTime, duration: defaultDuration }]);
    setDurations(prev => ({
      ...prev,
      [newIndex]: defaultDuration
    }));
    // Clear any error for the new slot
    setTimeSlotErrors(prev => ({
      ...prev,
      [newIndex]: ""
    }));
  };

  const handleRemoveTimeSlot = (index: number) => {
    setTimeSlots(prev => prev.filter((_, i) => i !== index));

    // Update the durations object by removing the entry and reindexing
    const updatedDurations = { ...durations };
    delete updatedDurations[index];

    // Reindex the durations
    const newDurations: { [key: number]: number } = {};
    timeSlots.filter((_, i) => i !== index).forEach((_, i) => {
      if (i < index) {
        newDurations[i] = updatedDurations[i] || 15;
      } else {
        newDurations[i] = updatedDurations[i + 1] || 15;
      }
    });

    setDurations(newDurations);

    // Update the errors object by removing the entry and reindexing
    const updatedErrors = { ...timeSlotErrors };
    delete updatedErrors[index];

    // Reindex the errors
    const newErrors: { [key: number]: string } = {};
    timeSlots.filter((_, i) => i !== index).forEach((_, i) => {
      if (i < index) {
        newErrors[i] = updatedErrors[i] || "";
      } else {
        newErrors[i] = updatedErrors[i + 1] || "";
      }
    });

    setTimeSlotErrors(newErrors);
  };

  // Validation function to check if end time is after start time
  const validateTimeSlot = (startTime: string, endTime: string): string | null => {
    const [startHour, startMinute] = startTime.split(":").map(Number);
    const [endHour, endMinute] = endTime.split(":").map(Number);

    const startTimeInMinutes = startHour * 60 + startMinute;
    const endTimeInMinutes = endHour * 60 + endMinute;

    // For same-day appointments, end time must be after start time
    if (endTimeInMinutes <= startTimeInMinutes) {
      return "End time must be after start time";
    }

    return null; // No error
  };

  const handleTimeChange = (index: number, field: 'startTime' | 'endTime', value: string) => {
    if (field === 'startTime') {
      // When start time changes, keep the current end time and validate
      setTimeSlots(prev => {
        const updated = [...prev];
        const slot = updated[index];

        // Validate the new start time against current end time
        const validationError = validateTimeSlot(value, slot.endTime);

        // Update error state
        setTimeSlotErrors(prevErrors => ({
          ...prevErrors,
          [index]: validationError || ""
        }));

        // If validation fails, update only the start time but don't calculate duration
        if (validationError) {
          updated[index] = {
            ...slot,
            startTime: value,
            duration: 0 // Set duration to 0 to indicate invalid state
          };
          return updated;
        }

        // Calculate duration based on current end time
        const [startHour, startMinute] = value.split(":").map(Number);
        const [endHour, endMinute] = slot.endTime.split(":").map(Number);

        const startTimeInMinutes = startHour * 60 + startMinute;
        const endTimeInMinutes = endHour * 60 + endMinute;
        const calculatedDuration = endTimeInMinutes - startTimeInMinutes;

        updated[index] = {
          ...slot,
          startTime: value,
          duration: calculatedDuration
        };

        // Also update the durations state
        setDurations(prevDurations => ({
          ...prevDurations,
          [index]: calculatedDuration
        }));

        return updated;
      });
    } else {
      // For end time, update the value and validate
      setTimeSlots(prev => {
        const updated = [...prev];
        const slot = updated[index];

        // Check if slot exists and has a valid startTime
        if (!slot || !slot.startTime) {
          console.error("Invalid slot or missing startTime at index:", index);
          // Return the previous state unchanged
          return prev;
        }

        // Validate the new end time against current start time
        const validationError = validateTimeSlot(slot.startTime, value);

        // Update error state
        setTimeSlotErrors(prevErrors => ({
          ...prevErrors,
          [index]: validationError || ""
        }));

        // If validation fails, update only the end time but don't calculate duration
        if (validationError) {
          updated[index] = {
            ...slot,
            endTime: value,
            duration: 0 // Set duration to 0 to indicate invalid state
          };
          return updated;
        }

        const [startHour, startMinute] = slot.startTime.split(":").map(Number);
        const [endHour, endMinute] = value.split(":").map(Number);

        const startTimeInMinutes = startHour * 60 + startMinute;
        const endTimeInMinutes = endHour * 60 + endMinute;
        const calculatedDuration = endTimeInMinutes - startTimeInMinutes;

        updated[index] = {
          ...slot,
          endTime: value,
          duration: calculatedDuration
        };

        // Also update the durations state
        setDurations(prevDurations => ({
          ...prevDurations,
          [index]: calculatedDuration
        }));

        return updated;
      });
    }
  };



  const handleApply = () => {
    if (selectedDate) {
      // Check if there are any validation errors
      const hasErrors = Object.values(timeSlotErrors).some(error => error !== "");

      if (hasErrors) {
        // Don't save if there are validation errors
        return;
      }

      // Check if any time slot has invalid duration (0 or negative)
      const hasInvalidDurations = timeSlots.some(slot => !slot.duration || slot.duration <= 0);

      if (hasInvalidDurations) {
        // Don't save if there are invalid durations
        return;
      }

      // Ensure all time slots have the correct duration before saving
      const updatedTimeSlots = timeSlots.map((slot) => {
        // Use the duration already calculated in the slot, or calculate it if missing
        let duration = slot.duration;

        if (!duration) {
          // Calculate duration from start and end times
          const [startHour, startMinute] = slot.startTime.split(":").map(Number);
          const [endHour, endMinute] = slot.endTime.split(":").map(Number);

          const startTimeInMinutes = startHour * 60 + startMinute;
          const endTimeInMinutes = endHour * 60 + endMinute;
          duration = endTimeInMinutes - startTimeInMinutes;
        }

        return {
          startTime: slot.startTime,
          endTime: slot.endTime,
          duration: duration
        };
      });

      const formattedDate = `${selectedDate.getDate()}, ${months[selectedDate.getMonth()]}, ${selectedDate.getFullYear()}`;
      onSave(formattedDate, updatedTimeSlots, specificDayId);
      onClose();
    }
  };

  const renderCalendar = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDay = getFirstDayOfMonth(year, month);
    const today = new Date();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-8 w-8"></div>);
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const isSelected = selectedDate &&
                         date.getDate() === selectedDate.getDate() &&
                         date.getMonth() === selectedDate.getMonth() &&
                         date.getFullYear() === selectedDate.getFullYear();

      // Check if the date is in the past or today (disable current date as well)
      const todayDateOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const dateOnly = new Date(year, month, day);
      const isPastOrTodayDate = dateOnly < todayDateOnly;

      days.push(
        <div
          key={day}
          className={`h-8 w-8 flex items-center justify-center rounded-full transition-colors
                     ${isPastOrTodayDate
                       ? 'text-gray-300 cursor-not-allowed'
                       : isSelected
                         ? 'bg-blue-600 text-white cursor-pointer'
                         : 'hover:bg-gray-100 cursor-pointer'}`}
          onClick={() => !isPastOrTodayDate && handleDateClick(day)}
          title={isPastOrTodayDate ? "Past and current dates cannot be selected" : ""}
        >
          {day}
        </div>
      );
    }

    return days;
  };

  if (!container || !open) {
    return null;
  }

  return ReactDOM.createPortal(
    <div
      className={`fixed inset-0 z-[99990] flex items-center justify-center bg-gray-800 bg-opacity-50 transition-opacity duration-300 ${
        open ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-lg transform transition-transform duration-300 overflow-hidden mx-4 my-6 sm:my-8 w-[80vh] max-w-md sm:max-w-md md:max-w-lg lg:max-w-xl max-h-[90vh] flex flex-col modal-container"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-xl font-bold"
          onClick={onClose}
          aria-label="Close modal"
        >
          <IoCloseCircleOutline className="w-5 h-5 md:w-7 md:h-7" />
        </button>

        <div className="flex flex-col h-full min-h-0">
          {/* Header section - fixed */}
          <div className="p-6 pb-4 flex-shrink-0">
            <h2 className="text-xl font-semibold mb-4">
              Pick The Dates For Your Custom Time Slots
            </h2>

            {/* Calendar Header */}
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={handlePrevMonth}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <CaretLeft size={24} />
              </button>
              <h3 className="text-lg font-medium">
                {months[currentDate.getMonth()]} {currentDate.getFullYear()}
              </h3>
              <button
                onClick={handleNextMonth}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <CaretRight size={24} />
              </button>
            </div>

            {/* Calendar Grid */}
            <div className="mb-6">
              <div className="grid grid-cols-7 gap-1 mb-2">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                  <div key={day} className="text-center text-sm font-medium">
                    {day}
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-7 gap-1">
                {renderCalendar()}
              </div>
            </div>
          </div>

          {/* Scrollable content section */}
          {selectedDate && (
            <div className="flex flex-col flex-grow min-h-0">
              <div className="px-6 pb-4 flex-grow overflow-y-auto">
                <div className="border-t pt-4">
                  <h3 className="text-base font-medium mb-4">
                    At what times are you available or not available?
                  </h3>
                  <div className="space-y-4 relative time-slots-container">
                    {timeSlots.map((slot, index) => (
                      <div key={index} className="mb-4">
                        <div className="flex items-center gap-4 flex-wrap sm:flex-nowrap">
                          <div className="flex flex-col sm:flex-row sm:items-center w-full gap-4">
                            <div className="flex items-center gap-2">
                              <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                                Start Time :
                              </label>
                              <TimePicker
                                value={slot.startTime}
                                onChange={(value: string) => handleTimeChange(index, 'startTime', value)}
                                className="w-25"
                              />
                            </div>
                            <div className="flex items-center gap-2">
                              <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                                End Time : 
                              </label>
                              <TimePicker
                                value={slot.endTime}
                                onChange={(value: string) => handleTimeChange(index, 'endTime', value)}
                                className="w-25"
                              />
                            </div>
                            <div className="flex items-center justify-end mt-2 sm:mt-0">
                              {index === timeSlots.length - 1 ? (
                                <button
                                  onClick={handleAddTimeSlot}
                                  className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                                  title="Add another time slot"
                                >
                                  <Plus size={20} />
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleRemoveTimeSlot(index)}
                                  className="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                                  title="Remove this time slot"
                                >
                                  <Trash size={20} />
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                        {/* Error message */}
                        {timeSlotErrors[index] && (
                          <div className="mt-1 text-sm text-red-600">
                            {timeSlotErrors[index]}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Action Buttons - fixed at bottom */}
              <div className="px-6 py-4 border-t bg-white flex-shrink-0">
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleApply}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Apply
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>,
    container
  );
};

export default DatePickerModal;
